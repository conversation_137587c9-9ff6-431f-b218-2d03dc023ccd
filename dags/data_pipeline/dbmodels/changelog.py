import datetime
from typing import Optional, Any

from sqlalchemy.ext.indexable import index_property
from sqlalchemy.orm import Mapped, mapped_column

from .base import Base, TableName
from .user import User
from .issue import Issue
from sqlalchemy import Column, BIGINT, String, ForeignKey, DateTime, Sequence, func, Index, UniqueConstraint
from sqlalchemy.dialects.postgresql import ARRAY, TEXT, JSONB, JSON, TIMESTAMP
from sqlalchemy.schema import FetchedValue


# Source: https://docs.sqlalchemy.org/en/20/orm/extensions/indexable.html
class pg_json_property(index_property):
    def __init__(self, attr_name, index, cast_type):
        super(pg_json_property, self).__init__(attr_name, index)
        self.cast_type = cast_type

    def expr(self, model):
        expr = super(pg_json_property, self).expr(model)
        return expr.astext.cast(self.cast_type)


class ChangeLog(Base):
    use_snake_case = False

    id = Column(BIGINT, nullable=False, primary_key=True)
    author = Column(String, ForeignKey(User.accountId), nullable=True)
    created = Column(TIMESTAMP(timezone=True), nullable=False)
    field = Column(String, nullable=False, primary_key=True)
    fieldtype = Column(String, nullable=False)
    fieldId = Column(String, nullable=True)
    from_ = Column(String, nullable=False, primary_key=True)
    fromString = Column(String, nullable=True)
    to = Column(String, nullable=False, primary_key=True)
    toString = Column(String, nullable=True)
    tmpFromAccountId = Column(TEXT, nullable=True)
    tmpToAccountId = Column(TEXT, nullable=True)
    issue_key = Column(String, ForeignKey(Issue.key), nullable=False)
    issue_id = Column(BIGINT, ForeignKey(Issue.id), nullable=False)


# class ChangelogJSON(Base):
#     use_snake_case = True
#     id = Column(BIGINT, primary_key=True, autoincrement=False)
#     author = Column(String, ForeignKey(User.accountId), nullable=True)
#     created = Column(DateTime(timezone=True), nullable=False)
#     items = Column(JSONB, nullable=False)
#     issue_key = Column(
#         String,
#         ForeignKey(Issue.key, deferrable=True, initially='DEFERRED'),
#         nullable=False, index=True
#     )
#     issue_id = Column(
#         BIGINT,
#         ForeignKey(Issue.id, deferrable=True, initially='DEFERRED'),
#         nullable=False, index=True
#     )
#     # Index properties for JSONB fields
#     # field = index_property(attr_name='items', index='field', default=None)
#     # fieldtype = index_property('items', 'fieldtype', default=None)
#     # fieldId = index_property('items', 'fieldId', default=None)
#     # from_value = pg_json_property('items', 'from', String)
#     # fromString = index_property('items', 'fromString', default=None)
#     # to = pg_json_property('items', 'to', String)
#     # toString = index_property('items', 'toString', default=None)


class ChangelogJSON(Base):
    use_snake_case = True

    # Primary key
    id: Mapped[int] = mapped_column(BIGINT, primary_key=True, autoincrement=False)

    # Foreign key relationships
    author: Mapped[Optional[str]] = mapped_column(
        String,
        ForeignKey("user.account_id"),  # Assuming snake_case table name
        nullable=True
    )

    # Timestamps
    created: Mapped[datetime.datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False
    )

    # JSONB column
    items: Mapped[dict[str, Any]] = mapped_column(JSONB, nullable=False)

    # Issue foreign keys with deferred constraints
    issue_key: Mapped[str] = mapped_column(
        String,
        ForeignKey(
            "issue.key",
            deferrable=True,
            initially='DEFERRED'
        ),
        nullable=False,
        index=True
    )

    issue_id: Mapped[int] = mapped_column(
        BIGINT,
        ForeignKey(
            "issue.id",
            deferrable=True,
            initially='DEFERRED'
        ),
        nullable=False,
        index=True
    )

    # Relationships (optional - add if you have User and Issue models)
    # author_user: Mapped[Optional["User"]] = relationship("User", back_populates="changelogs")
    # issue: Mapped["Issue"] = relationship("Issue", back_populates="changelogs")

    # Table-level indexes for JSONB fields
    __table_args__ = (
        # GIN index for general JSONB queries
        Index('ix_changelog_json_items_gin', 'items', postgresql_using='gin'),

        # Specific indexes for commonly queried JSON paths
        Index('ix_changelog_json_field', items['field'].astext),
        Index('ix_changelog_json_fieldtype', items['fieldtype'].astext),
        Index('ix_changelog_json_field_id', items['fieldId'].astext),
        Index('ix_changelog_json_from_string', items['fromString'].astext),
        Index('ix_changelog_json_to_string', items['toString'].astext),
    )

    # Property methods to access JSONB fields (replaces index_property and pg_json_property)
    @property
    def field(self) -> Optional[str]:
        return self.items.get('field')

    @property
    def fieldtype(self) -> Optional[str]:
        return self.items.get('fieldtype')

    @property
    def fieldId(self) -> Optional[str]:
        return self.items.get('fieldId')

    @property
    def from_value(self) -> Optional[str]:
        return self.items.get('from')

    @property
    def fromString(self) -> Optional[str]:
        return self.items.get('fromString')

    @property
    def to(self) -> Optional[str]:
        return self.items.get('to')

    @property
    def toString(self) -> Optional[str]:
        return self.items.get('toString')
