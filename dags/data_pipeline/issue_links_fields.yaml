# yaml-language-server: $schema=./schema/enhanced_fields.schema.json
fields:
  - id: id
    name: "Issue ID"
    custom: false
    datatype: string
    source_column: id
    target_column: id
    target_type: Int32
  - id: type
    name: "Link Type"
    custom: true
    datatype: array
    source_column: type.name
    target_column: type
    target_type: json
  - id: outwardIssue_id
    name: "Outward Issue ID"
    custom: false
    datatype: string
    source_column: outwardIssue.id
    target_column: outwardIssue_id
    target_type: Int32
  - id: outwardIssue_key
    name: "Outward Issue Key"
    custom: false
    datatype: string
    source_column: outwardIssue.key
    target_column: outwardIssue_key
    target_type: string[pyarrow]

  - id: inwardIssue_id
    name: "Inward Issue ID"
    custom: false
    datatype: string
    source_column: inwardIssue.id
    target_column: inwardIssue_id
    target_type: Int32

  - id: inwardIssue_key
    name: "Inward Issue Key"
    custom: false
    datatype: string
    source_column: inwardIssue.key
    target_column: inwardIssue_key
    target_type: string[pyarrow]

  - id: issue_key
    name: "Key Rename"
    custom: false
    datatype: string
    source_column: key
    target_column: issue_key
    target_type: string[pyarrow]

  - id: issue_id
    name: "Issue ID Final"
    custom: false
    datatype: string
    source_column: issue_id
    target_column: issue_id
    target_type: Int32

database_config:
  model: IssueLinks

drop-column-prefixes:
  - type.
  - outwardIssue.
  - inwardIssue.

drop-column-exceptions:
  - type.name
  - outwardIssue.id
  - outwardIssue.key
  - inwardIssue.id
  - inwardIssue.key