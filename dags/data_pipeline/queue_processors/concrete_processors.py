# coding=utf-8
"""
Concrete implementations of queue processors.

This module provides the refactored implementations of process_upsert_queue
and process_upsert_others using the new base class and extracted components.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from collections import defaultdict

import pandas as pd
from dependency_injector.wiring import inject, Provide
from dependency_injector import containers

from dags.data_pipeline.containers import <PERSON>ggerContainer, QueueContainer
from dags.data_pipeline.queue_processors.base_queue_processor import (
    AbstractQueueProcessor, BatchProcessingConfig, QueueProcessingResult
)
from dags.data_pipeline.queue_processors.batch_processing_strategy import (
    BatchProcessingStrategy, BatchProcessingMetrics, create_upsert_queue_strategy, create_upsert_others_strategy
)
from dags.data_pipeline.queue_processors.event_coordination import (
    enhanced_event_coordinator, referential_integrity_coordinator
)


class UpsertQueueProcessor(AbstractQueueProcessor):
    """
    Concrete implementation for process_upsert_queue.
    
    This processor handles the main issue queue with batch processing
    in chunks of 100 and early processing triggers from consume_issue completion.
    """
    
    def __init__(
        self,
        project_key: str,
        logger: Optional[logging.Logger] = None,
        batch_strategy: Optional[BatchProcessingStrategy] = None
    ):
        """
        Initialize the upsert queue processor.
        
        Args:
            project_key: Project key for database operations
            logger: Logger instance
            batch_strategy: Custom batch processing strategy
        """
        config = BatchProcessingConfig(
            batch_size=100,
            enable_early_processing=True,
            timeout_seconds=5.0
        )
        
        super().__init__(project_key, "queue_upsert_issue", config, logger)
        
        self.batch_strategy = batch_strategy or create_upsert_queue_strategy(logger)
        self.logger.info(f"UpsertQueueProcessor initialized with strategy: {self.batch_strategy.get_strategy_name()}")
    
    async def _initialize_processing(self, coordination_manager: Any) -> None:
        """Initialize processing-specific setup."""
        await enhanced_event_coordinator.register_processor("process_upsert_queue")
        self.logger.info("UpsertQueueProcessor initialized and registered")
    
    async def _should_process_early(self, coordination_manager: Any) -> bool:
        """Check if early processing should be triggered."""
        # Check for immediate processing trigger from consume_issue completion
        if await enhanced_event_coordinator.should_process_immediately():
            self.logger.info("Early processing triggered by consume_issue completion")
            await enhanced_event_coordinator.clear_immediate_processing_trigger()
            return True
        return False
    
    async def _should_terminate(self, coordination_manager: Any) -> bool:
        """Check if processing should terminate."""
        return await coordination_manager.should_terminate_consumer(self.queue_name)
    
    async def _should_process_batch(self) -> bool:
        """Check if batch processing should be triggered."""
        metrics = BatchProcessingMetrics(
            message_count=self.batch_count,
            total_records=sum(self.record_count.values()),
            queue_size=0  # Will be updated by caller if needed
        )
        
        return await self.batch_strategy.should_process_batch(
            metrics, self.consolidated_dataframes, None
        )
    
    async def _finalize_processing(self, coordination_manager: Any) -> None:
        """Finalize processing and cleanup."""
        # Process any remaining consolidated DataFrames
        if self.consolidated_dataframes:
            await self._process_consolidated_data()
        
        # Signal completion for referential integrity
        await enhanced_event_coordinator.signal_issue_processing_complete()
        await referential_integrity_coordinator.signal_parent_tables_committed()
        
        # Signal processor completion
        await enhanced_event_coordinator.signal_processor_complete("process_upsert_queue")
        
        self.logger.info(f"UpsertQueueProcessor finalized. Processed {self.message_count} messages")
    
    async def _process_consolidated_dataframes_impl(
        self,
        project_key: str,
        consolidated_dataframes: Dict[str, List[pd.DataFrame]],
        consolidated_configs: Dict[str, Dict[str, Any]],
        logger: logging.Logger,
        message_count: int
    ) -> None:
        """Implementation-specific DataFrame processing."""
        # Use the new DataFrameProcessor instead of the deprecated function
        from dags.data_pipeline.queue_processors.dataframe_processor import dataframe_processor

        await dataframe_processor.process_consolidated_dataframes(
            project_key,
            consolidated_dataframes,
            consolidated_configs,
            logger,
            message_count
        )


class UpsertOthersProcessor(AbstractQueueProcessor):
    """
    Concrete implementation for process_upsert_others.
    
    This processor handles specialized consumer queues (changelog, worklog, comment, issue_links)
    and waits for issue processing completion to maintain referential integrity.
    """
    
    def __init__(
        self,
        project_key: str,
        logger: Optional[logging.Logger] = None,
        batch_strategy: Optional[BatchProcessingStrategy] = None
    ):
        """
        Initialize the upsert others processor.
        
        Args:
            project_key: Project key for database operations
            logger: Logger instance
            batch_strategy: Custom batch processing strategy
        """
        config = BatchProcessingConfig(
            batch_size=1000,  # Larger batch size for others
            enable_early_processing=True,
            timeout_seconds=5.0
        )
        
        super().__init__(project_key, "queue_upsert_others", config, logger)
        
        self.batch_strategy = batch_strategy or create_upsert_others_strategy(logger)
        self.logger.info(f"UpsertOthersProcessor initialized with strategy: {self.batch_strategy.get_strategy_name()}")
    
    async def _initialize_processing(self, coordination_manager: Any) -> None:
        """Initialize processing-specific setup."""
        await enhanced_event_coordinator.register_processor("process_upsert_others")
        self.logger.info("UpsertOthersProcessor initialized and registered")
    
    async def _should_process_early(self, coordination_manager: Any) -> bool:
        """Check if early processing should be triggered."""
        # Check for immediate processing trigger
        if await enhanced_event_coordinator.should_process_immediately():
            self.logger.info("Early processing triggered for others queue")
            # Don't clear the trigger here as other processors might need it
            return True
        return False
    
    async def _should_terminate(self, coordination_manager: Any) -> bool:
        """
        Check if processing should terminate.

        UpsertOthersProcessor should only terminate when:
        1. All specialized consumers (consume_worklog, consume_changelog, etc.) are complete
        2. The queue is empty
        3. No more messages are expected
        """
        # First check if all specialized consumers are complete
        specialized_consumers_complete = await self._wait_for_specialized_consumers_completion(coordination_manager)

        if not specialized_consumers_complete:
            return False

        # Then check the standard termination condition
        return await coordination_manager.should_terminate_consumer(self.queue_name)
    
    async def _should_process_batch(self) -> bool:
        """Check if batch processing should be triggered."""
        # UpsertOthersProcessor primarily uses event-based processing
        return False
    
    async def _finalize_processing(self, coordination_manager: Any) -> None:
        """Finalize processing and cleanup."""
        # Wait for parent tables to be committed before processing remaining data
        if self.consolidated_dataframes:
            self.logger.info("Waiting for parent tables commitment before processing remaining data")
            
            # Wait for parent tables to be committed (with timeout)
            parent_committed = await referential_integrity_coordinator.wait_for_parent_commitment(timeout=30.0)
            
            if parent_committed:
                await self._process_consolidated_data()
                self.logger.info("Processed remaining consolidated DataFrames after parent commitment")
            else:
                self.logger.warning("Timeout waiting for parent commitment - processing anyway")
                await self._process_consolidated_data()
        
        # Signal processor completion
        await enhanced_event_coordinator.signal_processor_complete("process_upsert_others")
        
        self.logger.info(f"UpsertOthersProcessor finalized. Processed {self.message_count} messages")

    async def _wait_for_specialized_consumers_completion(self, coordination_manager: Any) -> bool:
        """
        Wait for all specialized consumers to complete before allowing termination.

        This ensures that consume_worklog, consume_changelog, consume_issuelinks, etc.
        have finished putting all their messages in the queue before we terminate.

        Returns:
            bool: True if all specialized consumers are complete, False otherwise
        """
        # Check if all specialized consumer completion events are set
        specialized_events = [
            coordination_manager.consume_worklog_complete,
            coordination_manager.consume_changelog_complete,
            coordination_manager.consume_comment_complete,
            coordination_manager.consume_issue_links_complete,
            coordination_manager.consume_issue_complete_event
        ]

        # Check if all events are set
        all_complete = all(event.is_set() for event in specialized_events)

        if not all_complete:
            # Log which consumers are still running
            incomplete_consumers = []
            if not coordination_manager.consume_worklog_complete.is_set():
                incomplete_consumers.append("consume_worklog")
            if not coordination_manager.consume_changelog_complete.is_set():
                incomplete_consumers.append("consume_changelog")
            if not coordination_manager.consume_comment_complete.is_set():
                incomplete_consumers.append("consume_comment")
            if not coordination_manager.consume_issue_links_complete.is_set():
                incomplete_consumers.append("consume_issue_links")
            if not coordination_manager.consume_issue_complete_event.is_set():
                incomplete_consumers.append("consume_issue")

            self.logger.debug(f"Waiting for specialized consumers to complete: {incomplete_consumers}")
            return False

        self.logger.info("All specialized consumers have completed - ready for termination")
        return True
    
    async def _process_consolidated_dataframes_impl(
        self,
        project_key: str,
        consolidated_dataframes: Dict[str, List[pd.DataFrame]],
        consolidated_configs: Dict[str, Dict[str, Any]],
        logger: logging.Logger,
        message_count: int
    ) -> None:
        """Implementation-specific DataFrame processing."""
        # Use the new DataFrameProcessor instead of the deprecated function
        from dags.data_pipeline.queue_processors.dataframe_processor import dataframe_processor

        await dataframe_processor.process_consolidated_dataframes(
            project_key,
            consolidated_dataframes,
            consolidated_configs,
            logger,
            message_count
        )


# Factory functions for creating processors
@inject
def create_upsert_queue_processor(
    project_key: str,
    logger: logging.Logger = Provide[LoggerContainer.logger]
) -> UpsertQueueProcessor:
    """Create a configured UpsertQueueProcessor."""
    return UpsertQueueProcessor(project_key, logger)


@inject
def create_upsert_others_processor(
    project_key: str,
    logger: logging.Logger = Provide[LoggerContainer.logger]
) -> UpsertOthersProcessor:
    """Create a configured UpsertOthersProcessor."""
    return UpsertOthersProcessor(project_key, logger)


# Refactored function implementations that use the new processors
@inject
async def process_upsert_queue_refactored(
    project_key: str,
    q_container: containers.DynamicContainer = Provide[QueueContainer],
    logger: logging.Logger = Provide[LoggerContainer.logger]
) -> QueueProcessingResult:
    """
    Refactored process_upsert_queue using the new processor architecture.
    
    Args:
        project_key: Project key for database operations
        q_container: Queue container for dependency injection
        logger: Logger instance
        
    Returns:
        QueueProcessingResult with processing statistics
    """
    # Import coordination manager
    from dags.data_pipeline.scalable_coordination import coordination_manager
    
    # Create processor
    processor = create_upsert_queue_processor(project_key, logger)
    
    # Get queue
    queue_upsert_issue = q_container.queue_selector()["queue_upsert_issue"]
    
    # Process queue
    result = await processor.process_queue(queue_upsert_issue, coordination_manager, q_container)
    
    logger.info(f"process_upsert_queue completed: {result.messages_processed} messages, {result.batches_processed} batches")
    return result


@inject
async def process_upsert_others_refactored(
    project_key: str,
    q_container: containers.DynamicContainer = Provide[QueueContainer],
    logger: logging.Logger = Provide[LoggerContainer.logger]
) -> QueueProcessingResult:
    """
    Refactored process_upsert_others using the new processor architecture.
    
    Args:
        project_key: Project key for database operations
        q_container: Queue container for dependency injection
        logger: Logger instance
        
    Returns:
        QueueProcessingResult with processing statistics
    """
    # Import coordination manager
    from dags.data_pipeline.scalable_coordination import coordination_manager
    
    # Create processor
    processor = create_upsert_others_processor(project_key, logger)
    
    # Get queue
    queue_upsert_others = q_container.queue_selector()["queue_upsert_others"]
    
    # Process queue
    result = await processor.process_queue(queue_upsert_others, coordination_manager, q_container)
    
    logger.info(f"process_upsert_others completed: {result.messages_processed} messages, {result.batches_processed} batches")
    return result
