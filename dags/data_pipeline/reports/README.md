# JIRA Processing Reports

A comprehensive reporting system for JIRA data processing pipelines that generates interactive HTML reports with executive KPIs and operational details.

## Features

### 📊 Executive Dashboard
- **High-level KPIs**: Total records, accuracy percentages, API success rates
- **Color-coded status**: Green (<0.1%), <PERSON> (0.1-1%), Red (>1%) for accuracy
- **Processing overview**: Total time, throughput metrics, system health

### 🏭 Producer Metrics
- **JQL Query tracking**: Full query text and execution details
- **Accuracy analysis**: Records processed vs estimated with difference percentages
- **Performance metrics**: Processing time, waiting time, CPU time
- **Timeline visualization**: Gantt-style charts showing producer execution

### 🔄 Consumer Metrics  
- **Message throughput**: Messages received/sent per consumer
- **Scaling events**: Dynamic scaling up/down based on queue size
- **Processing efficiency**: Time per message, queue processing rates
- **Consumer types**: Issues, changelog, worklog, comments, issue links

### 🌐 API Call Analytics
- **fetch_with_retries statistics**: Success rates, retry distribution
- **Endpoint analysis**: Per-endpoint performance and error rates
- **Timing metrics**: Average time excluding/including wait times
- **Error classification**: Timeout, HTTP errors, connection issues

### 📱 Interactive Features
- **Responsive design**: Works on desktop and mobile devices
- **Tabbed interface**: Executive, Producers, Consumers, API, Operations views
- **Interactive charts**: Plotly-based visualizations with zoom/pan
- **Exportable HTML**: Self-contained files with all dependencies

## Quick Start

### 1. Simple Integration
Add one line to your `process_jira_issues` function:

```python
from dags.data_pipeline.reports import setup_comprehensive_reporting

async def process_jira_issues(project_key: str, scope: str, initial_load: bool):
    # Enable comprehensive reporting
    setup_comprehensive_reporting(project_key)
    
    # Your existing code continues unchanged...
    success = await _process_issues_with_producers_consumers(...)
    
    # Report automatically generated at exit
    return success
```

### 2. Generate Sample Report
```python
from dags.data_pipeline.reports import generate_sample_report

# Create a sample report with mock data
report_path = generate_sample_report("PLAT")
print(f"Sample report: {report_path}")
```

### 3. Manual Metrics Tracking
```python
from dags.data_pipeline.reports import track_producer_metrics, track_consumer_metrics

# Track producer manually
track_producer_metrics(
    producer_name="producer_0",
    jql_query="project = PLAT AND updated >= '2024-01-01'",
    total_records_processed=1250,
    total_records_estimate=1200,
    processing_time=45.2,
    waiting_time=2.1,
    cpu_time=42.8,
    start_time=start_time,
    end_time=end_time
)

# Track consumer manually  
track_consumer_metrics(
    consumer_name="consumer_apple",
    consumer_type="consume_issues",
    messages_received=2140,
    messages_sent=2140,
    processing_time=78.5,
    scaling_events=[{"event": "scaled_up", "reason": "queue_size_exceeded"}],
    start_time=start_time,
    end_time=end_time
)
```

## Report Structure

### Executive Summary
- **Total Records**: Processed vs estimated with accuracy percentage
- **API Success Rate**: Overall success rate across all endpoints
- **Processing Time**: Total time spent in producer functions
- **Visual Charts**: Producer performance overview, API distribution

### Producer Details
- **Performance Table**: All producers with JQL, records, accuracy, timing
- **Color Coding**: Green/Amber/Red based on accuracy thresholds
- **Timeline Chart**: Visual representation of producer execution overlap

### Consumer Details  
- **Throughput Table**: Messages received/sent, processing time, scaling events
- **Performance Charts**: Consumer throughput, scaling events timeline
- **Status Tracking**: Completed/Running/Failed status for each consumer

### API Metrics
- **Statistics Table**: Per-endpoint success rates, retry rates, timing
- **Retry Distribution**: Histogram of retry counts across all calls
- **Error Analysis**: Pie chart of error types (timeout, HTTP, connection)

### Operations Details
- **System Resources**: Memory, CPU, connection usage over time
- **Processing Timeline**: Complete timeline of all processing activities
- **Event Logs**: Detailed log of all processing events and errors

## Architecture

### Core Components
- **MetricsCollector**: Central collection point for all metrics
- **JiraProcessingReportGenerator**: HTML report generation with Plotly charts
- **Integration patches**: Automatic instrumentation of existing functions

### Data Flow
1. **Collection**: Metrics collected during processing via decorators/patches
2. **Aggregation**: Data aggregated in global metrics collector
3. **Generation**: HTML report generated at process exit
4. **Export**: Self-contained HTML file with embedded charts and CSS

### Automatic Instrumentation
- **fetch_with_retries**: Automatically patched to track API calls
- **Producer functions**: Wrapped to track JQL execution and timing
- **Consumer functions**: Instrumented to track message processing
- **Exit handler**: Automatic report generation when process completes

## File Structure

```
dags/data_pipeline/reports/
├── __init__.py                          # Main module exports
├── jira_processing_report_generator.py  # Core report generation
├── metrics_integration.py              # Context managers and decorators  
├── integration_patch.py                # Automatic function patching
├── usage_example.py                    # Examples and integration guide
└── README.md                           # This documentation
```

## Dependencies

- **plotly**: Interactive charts and visualizations
- **bootstrap**: Responsive CSS framework (CDN)
- **Standard library**: json, time, datetime, pathlib, asyncio

## Output

Reports are saved as `jira_processing_report_{PROJECT_KEY}_{TIMESTAMP}.html` in the current directory or specified output directory.

Example: `jira_processing_report_PLAT_20241228_143052.html`

## Color Coding

- **Green**: Accuracy < 0.1% (excellent)
- **Amber**: Accuracy 0.1-1% (acceptable)  
- **Red**: Accuracy > 1% (needs attention)

## Browser Compatibility

Reports work in all modern browsers with JavaScript enabled. Charts require internet connection for Plotly CDN (or can be modified to use local Plotly installation).
