"""
JIRA Processing Reports Module

This module provides comprehensive reporting capabilities for JIRA data processing,
including producer/consumer metrics, API call statistics, and interactive HTML reports.

Key Features:
- Executive KPI dashboards with color-coded status indicators
- Detailed producer metrics (JQL queries, accuracy, timing)
- Consumer throughput and scaling event tracking
- API call success rates, retry distribution, and error analysis
- Interactive Plotly charts and responsive design
- Automatic metrics collection via decorators and patches
- Self-contained HTML reports with all dependencies

Quick Start:
    from dags.data_pipeline.reports import setup_comprehensive_reporting
    
    # At the start of process_jira_issues
    setup_comprehensive_reporting("PLAT")
    
    # Report will be automatically generated at process exit
"""

from .jira_processing_report_generator import (
    MetricsCollector,
    JiraProcessingReportGenerator,
    ProducerMetrics,
    ConsumerMetrics,
    ApiCallMetrics,
    track_producer_metrics,
    track_consumer_metrics,
    track_api_call,
    create_report_at_exit,
    global_metrics_collector
)

from .metrics_integration import (
    MetricsTracker,
    track_api_calls,
    producer_metrics_context,
    consumer_metrics_context,
    setup_exit_report_generation
)

from .integration_patch import (
    FetchWithRetriesMetricsTracker,
    ProcessingMetricsCollector,
    patch_fetch_with_retries,
    setup_metrics_tracking,
    global_processing_metrics
)

from .usage_example import (
    enhanced_process_jira_issues,
    manual_report_generation_example,
    integration_instructions
)


def setup_comprehensive_reporting(project_key: str, output_dir: str = None):
    """
    One-line setup for comprehensive JIRA processing reporting.

    This function sets up:
    - Automatic metrics collection for fetch_with_retries
    - Producer and consumer tracking
    - Report generation at process exit

    Args:
        project_key: JIRA project key for the report
        output_dir: Directory to save reports (default: current directory)

    Returns:
        Dictionary of original functions (for restoration if needed)
    """
    print(f"🚀 Setting up comprehensive reporting for {project_key}")

    # Setup exit report generation
    if output_dir:
        setup_exit_report_generation(project_key, output_dir)
    else:
        setup_exit_report_generation(project_key)

    # Setup comprehensive metrics tracking
    original_functions = setup_metrics_tracking(project_key)

    print(f"✅ Comprehensive reporting system active for {project_key}")
    print(f"📊 Report will be generated automatically at process exit")

    return original_functions


def generate_sample_report(project_key: str = "SAMPLE") -> str:
    """
    Generate a sample report with mock data for testing.
    
    Args:
        project_key: Project key to use in the sample report
        
    Returns:
        Path to the generated sample report
    """
    return manual_report_generation_example()


# Export main functions for easy access
__all__ = [
    # Core classes
    'MetricsCollector',
    'JiraProcessingReportGenerator',
    'ProducerMetrics',
    'ConsumerMetrics',
    'ApiCallMetrics',
    
    # Tracking functions
    'track_producer_metrics',
    'track_consumer_metrics',
    'track_api_call',
    
    # Report generation
    'create_report_at_exit',
    'global_metrics_collector',
    
    # Integration helpers
    'MetricsTracker',
    'producer_metrics_context',
    'consumer_metrics_context',
    'setup_exit_report_generation',
    
    # Patching system
    'FetchWithRetriesMetricsTracker',
    'ProcessingMetricsCollector',
    'patch_fetch_with_retries',
    'setup_metrics_tracking',
    'global_processing_metrics',
    
    # High-level functions
    'setup_comprehensive_reporting',
    'generate_sample_report',
    'enhanced_process_jira_issues',
    'integration_instructions'
]


# Version info
__version__ = "1.0.0"
__author__ = "JIRA Processing Team"
__description__ = "Comprehensive reporting system for JIRA data processing pipelines"
