"""
Integration Test for JIRA Processing Reports

This script tests the integration with your existing process_jira_issues function
to ensure metrics are being captured correctly.
"""

import asyncio
import time
from datetime import datetime
from pathlib import Path

from dags.data_pipeline.reports.integration_patch import setup_metrics_tracking, global_processing_metrics
from dags.data_pipeline.reports.jira_processing_report_generator import create_report_at_exit


async def test_integration():
    """Test the integration with mock function calls"""
    
    print("🧪 Testing JIRA Processing Report Integration")
    print("=" * 50)
    
    # Setup metrics tracking
    project_key = "TEST"
    original_functions = setup_metrics_tracking(project_key)
    
    print(f"✅ Setup complete for project: {project_key}")
    
    # Simulate some producer activity
    print("\n📊 Simulating producer activity...")
    
    # Manually add some producer metrics to test
    global_processing_metrics.start_producer_tracking(
        "producer_0", 
        "project = TEST AND updated >= '2024-01-01'", 
        1000
    )
    
    # Simulate processing
    await asyncio.sleep(0.1)
    global_processing_metrics.update_producer_progress("producer_0", 950)
    
    global_processing_metrics.finish_producer_tracking("producer_0", "completed")
    
    # Add another producer
    global_processing_metrics.start_producer_tracking(
        "producer_1",
        "project = TEST AND created >= '2024-01-15'",
        500
    )
    
    await asyncio.sleep(0.05)
    global_processing_metrics.update_producer_progress("producer_1", 523)
    global_processing_metrics.finish_producer_tracking("producer_1", "completed")
    
    print("✅ Producer simulation complete")
    
    # Simulate consumer activity
    print("\n🔄 Simulating consumer activity...")
    
    consumers = [
        ("consumer_apple_issues", "consume_issues"),
        ("changelog_processor", "consume_changelog"),
        ("worklog_processor", "consume_worklog"),
        ("comment_processor", "consume_comment"),
        ("issue_links_processor", "consume_issue_links")
    ]
    
    for consumer_name, consumer_type in consumers:
        global_processing_metrics.start_consumer_tracking(consumer_name, consumer_type)
        
        # Simulate message processing
        await asyncio.sleep(0.02)
        global_processing_metrics.update_consumer_progress(
            consumer_name, 
            messages_received=100 + hash(consumer_name) % 200,
            messages_sent=100 + hash(consumer_name) % 200
        )
        
        global_processing_metrics.finish_consumer_tracking(consumer_name, "completed")
    
    print("✅ Consumer simulation complete")
    
    # Check collected metrics
    print("\n📈 Checking collected metrics...")
    
    producer_count = len(global_processing_metrics.producer_stats)
    consumer_count = len(global_processing_metrics.consumer_stats)
    
    print(f"📊 Producers tracked: {producer_count}")
    print(f"🔄 Consumers tracked: {consumer_count}")
    
    if producer_count > 0:
        print("✅ Producer metrics collection working")
        for name, stats in global_processing_metrics.producer_stats.items():
            print(f"  - {name}: {stats['records_processed']} records")
    else:
        print("❌ No producer metrics collected")
    
    if consumer_count > 0:
        print("✅ Consumer metrics collection working")
        for name, stats in global_processing_metrics.consumer_stats.items():
            print(f"  - {name}: {stats['messages_received']} messages")
    else:
        print("❌ No consumer metrics collected")
    
    # Generate test report
    print("\n📄 Generating test report...")
    
    try:
        report_path = create_report_at_exit(project_key, Path.cwd())
        
        if Path(report_path).exists():
            file_size = Path(report_path).stat().st_size
            print(f"✅ Test report generated: {report_path}")
            print(f"📄 File size: {file_size:,} bytes")
            
            # Basic validation
            with open(report_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            checks = [
                ("Project key in report", project_key in content),
                ("Producer data", "producer_0" in content),
                ("Consumer data", "consumer_apple" in content),
                ("HTML structure", "<html" in content and "</html>" in content),
                ("Charts", "Plotly.newPlot" in content)
            ]
            
            print("\n🔍 Report validation:")
            all_good = True
            for check_name, passed in checks:
                status = "✅" if passed else "❌"
                print(f"  {status} {check_name}")
                if not passed:
                    all_good = False
            
            if all_good:
                print(f"\n🎉 Integration test PASSED!")
                print(f"🌐 Open report: file://{Path(report_path).absolute()}")
                return True
            else:
                print(f"\n⚠️  Some validations failed")
                return False
                
        else:
            print(f"❌ Report file not found: {report_path}")
            return False
            
    except Exception as e:
        print(f"❌ Report generation failed: {e}")
        return False


def print_integration_instructions():
    """Print instructions for using the fixed integration"""
    
    instructions = """
    
🔧 UPDATED INTEGRATION INSTRUCTIONS
================================

The integration has been fixed to properly capture metrics from your existing functions.

1. ADD TO YOUR process_jira_issues FUNCTION:

```python
from dags.data_pipeline.reports import setup_comprehensive_reporting

async def process_jira_issues(project_key: str, scope: str, initial_load: bool):
    # Add this line at the beginning
    setup_comprehensive_reporting(project_key)
    
    # Your existing code continues unchanged
    success = await _process_issues_with_producers_consumers(...)
    
    # Report will be automatically generated at exit
    return success
```

2. WHAT THE INTEGRATION NOW CAPTURES:

✅ Producer Metrics:
   - JQL queries from get_issues_from_jira_jql
   - Processing times and record estimates
   - Success/failure status

✅ Consumer Metrics:
   - All consumer types (issues, changelog, worklog, comment, issue_links)
   - Processing times and message counts
   - Success/failure status

✅ API Metrics:
   - All fetch_with_retries calls
   - Success rates, retry counts, timing
   - Error classification

✅ Report Features:
   - Executive dashboard with color-coded KPIs
   - Detailed producer/consumer tables
   - Interactive charts with Plotly
   - Responsive design for all devices

3. TROUBLESHOOTING:

If you still see zero records:
- Check console output for "📊 Started tracking..." messages
- Ensure setup_comprehensive_reporting() is called before any processing
- Check that your functions are actually being called

4. REPORT LOCATION:

Reports are saved as: jira_processing_report_{PROJECT_KEY}_{TIMESTAMP}.html
in your current working directory.

5. RETRY DISTRIBUTION CHART:

The chart now properly shows:
- Green bars for 0 retries (successful first attempt)
- Yellow bars for 1 retry
- Red bars for 2+ retries
- "No retry data available" if all calls succeeded without retries
"""
    
    print(instructions)


if __name__ == "__main__":
    # Run integration test
    success = asyncio.run(test_integration())
    
    print("\n" + "="*60)
    if success:
        print("🎯 INTEGRATION TEST PASSED")
    else:
        print("❌ INTEGRATION TEST FAILED")
    
    print_integration_instructions()
    print("="*60)
