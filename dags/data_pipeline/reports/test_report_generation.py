"""
Test Script for JIRA Processing Report Generation

This script demonstrates the reporting system with realistic test data
and validates that all components work correctly.
"""

import asyncio
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any
from dags.data_pipeline.reports.jira_processing_report_generator import (
    MetricsCollector, JiraProcessingReportGenerator,
    ProducerMetrics, ConsumerMetrics, ApiCallMetrics
)


def create_realistic_test_data() -> MetricsCollector:
    """Create realistic test data for report generation"""
    collector = MetricsCollector()
    
    # Simulate processing start time
    base_time = datetime.now() - timedelta(minutes=15)
    
    # Producer metrics - simulate multiple JQL queries
    producers = [
        {
            "name": "producer_0",
            "jql": "project = PLAT AND updated >= '2024-01-01' ORDER BY updated DESC",
            "estimate": 1200,
            "actual": 1247,
            "duration": 45.2
        },
        {
            "name": "producer_1", 
            "jql": "project = PLAT AND created >= '2024-01-15' AND status != Closed",
            "estimate": 850,
            "actual": 823,
            "duration": 32.7
        },
        {
            "name": "producer_2",
            "jql": "project = PLAT AND assignee = currentUser() AND resolution = Unresolved",
            "estimate": 450,
            "actual": 467,
            "duration": 18.9
        }
    ]
    
    for i, prod in enumerate(producers):
        start_time = base_time + timedelta(seconds=i*5)
        end_time = start_time + timedelta(seconds=prod["duration"])
        
        producer_metrics = ProducerMetrics(
            producer_name=prod["name"],
            jql_query=prod["jql"],
            total_records_processed=prod["actual"],
            total_records_estimate=prod["estimate"],
            difference_percentage=abs(prod["actual"] - prod["estimate"]) / prod["estimate"] * 100,
            processing_time=prod["duration"],
            waiting_time=2.1 + i * 0.5,
            cpu_time=prod["duration"] * 0.85,
            start_time=start_time,
            end_time=end_time,
            status="completed"
        )
        collector.add_producer_metrics(producer_metrics)
    
    # Consumer metrics - simulate different consumer types
    consumers = [
        {
            "name": "consumer_apple",
            "type": "consume_issues",
            "received": 2537,
            "sent": 2537,
            "duration": 78.5,
            "scaling": [
                {"timestamp": base_time + timedelta(seconds=30), "event": "scaled_up", "reason": "queue_size_exceeded"},
                {"timestamp": base_time + timedelta(seconds=120), "event": "scaled_down", "reason": "queue_stabilized"}
            ]
        },
        {
            "name": "changelog_processor",
            "type": "consume_changelog", 
            "received": 1247,
            "sent": 1247,
            "duration": 34.2,
            "scaling": []
        },
        {
            "name": "worklog_processor",
            "type": "consume_worklog",
            "received": 892,
            "sent": 892,
            "duration": 28.7,
            "scaling": [
                {"timestamp": base_time + timedelta(seconds=45), "event": "scaled_up", "reason": "high_throughput_required"}
            ]
        },
        {
            "name": "comment_processor",
            "type": "consume_comment",
            "received": 1534,
            "sent": 1534,
            "duration": 41.3,
            "scaling": []
        },
        {
            "name": "issue_links_processor",
            "type": "consume_issue_links",
            "received": 678,
            "sent": 678,
            "duration": 19.8,
            "scaling": []
        }
    ]
    
    for i, cons in enumerate(consumers):
        start_time = base_time + timedelta(seconds=10 + i*8)
        end_time = start_time + timedelta(seconds=cons["duration"])
        
        consumer_metrics = ConsumerMetrics(
            consumer_name=cons["name"],
            consumer_type=cons["type"],
            messages_received=cons["received"],
            messages_sent=cons["sent"],
            processing_time=cons["duration"],
            scaling_events=cons["scaling"],
            start_time=start_time,
            end_time=end_time,
            status="completed"
        )
        collector.add_consumer_metrics(consumer_metrics)
    
    # API metrics - simulate different endpoints with realistic patterns
    api_endpoints = [
        {
            "endpoint": "api/3/search/jql",
            "total": 67,
            "success_no_retry": 58,
            "success_with_retry": 7,
            "failed": 2,
            "avg_time_excl": 0.245,
            "avg_time_incl": 0.312,
            "retries": {0: 58, 1: 5, 2: 2, 3: 2},
            "errors": {"TIMEOUT": 1, "HTTP_ERROR": 1}
        },
        {
            "endpoint": "api/3/issue/{issueKey}/changelog",
            "total": 1247,
            "success_no_retry": 1198,
            "success_with_retry": 45,
            "failed": 4,
            "avg_time_excl": 0.156,
            "avg_time_incl": 0.189,
            "retries": {0: 1198, 1: 38, 2: 7, 3: 4},
            "errors": {"RATE_LIMIT": 2, "CONNECTION_ERROR": 2}
        },
        {
            "endpoint": "api/3/issue/{issueKey}/worklog",
            "total": 892,
            "success_no_retry": 867,
            "success_with_retry": 23,
            "failed": 2,
            "avg_time_excl": 0.134,
            "avg_time_incl": 0.167,
            "retries": {0: 867, 1: 20, 2: 3, 3: 2},
            "errors": {"TIMEOUT": 1, "HTTP_ERROR": 1}
        },
        {
            "endpoint": "api/3/issue/{issueKey}/comment",
            "total": 1534,
            "success_no_retry": 1489,
            "success_with_retry": 42,
            "failed": 3,
            "avg_time_excl": 0.142,
            "avg_time_incl": 0.178,
            "retries": {0: 1489, 1: 35, 2: 7, 3: 3},
            "errors": {"RATE_LIMIT": 2, "CONNECTION_ERROR": 1}
        }
    ]
    
    for api in api_endpoints:
        api_metrics = ApiCallMetrics(
            endpoint=api["endpoint"],
            total_requests=api["total"],
            successful_without_retries=api["success_no_retry"],
            successful_with_retries=api["success_with_retry"],
            failed_requests=api["failed"],
            average_time_excluding_wait=api["avg_time_excl"],
            average_time_including_wait=api["avg_time_incl"],
            retry_distribution=api["retries"],
            error_types=api["errors"]
        )
        collector.api_metrics[api["endpoint"]] = api_metrics
    
    collector.finalize()
    return collector


def test_report_generation():
    """Test the complete report generation process"""
    print("🧪 Testing JIRA Processing Report Generation")
    print("=" * 50)
    
    # Create test data
    print("📊 Creating realistic test data...")
    collector = create_realistic_test_data()
    
    # Validate data
    print(f"✅ Created {len(collector.producer_metrics)} producer metrics")
    print(f"✅ Created {len(collector.consumer_metrics)} consumer metrics") 
    print(f"✅ Created {len(collector.api_metrics)} API endpoint metrics")
    
    # Generate report
    print("\n🏗️  Generating HTML report...")
    generator = JiraProcessingReportGenerator(collector, "PLAT")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_path = Path(f"test_jira_report_PLAT_{timestamp}.html")
    
    report_path = generator.generate_html_report(output_path)
    
    # Validate output
    if Path(report_path).exists():
        file_size = Path(report_path).stat().st_size
        print(f"✅ Report generated successfully: {report_path}")
        print(f"📄 File size: {file_size:,} bytes")
        
        # Basic content validation
        with open(report_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        validations = [
            ("HTML structure", "<html" in content and "</html>" in content),
            ("Bootstrap CSS", "bootstrap" in content),
            ("Plotly JS", "plotly" in content),
            ("Project key", "PLAT" in content),
            ("Producer data", "producer_0" in content),
            ("Consumer data", "consumer_apple" in content),
            ("API data", "api/3/search/jql" in content),
            ("Interactive charts", "Plotly.newPlot" in content)
        ]
        
        print("\n🔍 Content validation:")
        for check, passed in validations:
            status = "✅" if passed else "❌"
            print(f"  {status} {check}")
        
        all_passed = all(passed for _, passed in validations)
        
        if all_passed:
            print(f"\n🎉 All tests passed! Report ready at: {report_path}")
            print(f"🌐 Open in browser: file://{Path(report_path).absolute()}")
            return report_path
        else:
            print(f"\n⚠️  Some validations failed. Check report content.")
            return None
            
    else:
        print(f"❌ Report generation failed - file not found: {report_path}")
        return None


def performance_test():
    """Test report generation performance with larger datasets"""
    print("\n⚡ Performance Testing")
    print("=" * 30)
    
    start_time = time.time()
    
    # Create larger dataset
    collector = MetricsCollector()
    
    # Generate 50 producers
    base_time = datetime.now() - timedelta(hours=1)
    for i in range(50):
        producer = ProducerMetrics(
            producer_name=f"producer_{i}",
            jql_query=f"project = PLAT AND updated >= '2024-01-{i+1:02d}'",
            total_records_processed=1000 + i*50,
            total_records_estimate=1000 + i*45,
            difference_percentage=abs((1000 + i*50) - (1000 + i*45)) / (1000 + i*45) * 100,
            processing_time=30.0 + i*2,
            waiting_time=1.0 + i*0.1,
            cpu_time=28.0 + i*1.8,
            start_time=base_time + timedelta(seconds=i*10),
            end_time=base_time + timedelta(seconds=i*10 + 30 + i*2),
            status="completed"
        )
        collector.add_producer_metrics(producer)
    
    # Generate 20 consumers
    for i in range(20):
        consumer = ConsumerMetrics(
            consumer_name=f"consumer_{i}",
            consumer_type=f"consume_type_{i%5}",
            messages_received=500 + i*100,
            messages_sent=500 + i*100,
            processing_time=20.0 + i*3,
            scaling_events=[],
            start_time=base_time + timedelta(seconds=i*15),
            end_time=base_time + timedelta(seconds=i*15 + 20 + i*3),
            status="completed"
        )
        collector.add_consumer_metrics(consumer)
    
    collector.finalize()
    
    # Generate report
    generator = JiraProcessingReportGenerator(collector, "PERF_TEST")
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_path = Path(f"perf_test_report_{timestamp}.html")
    
    generation_start = time.time()
    report_path = generator.generate_html_report(output_path)
    generation_time = time.time() - generation_start
    
    total_time = time.time() - start_time
    
    if Path(report_path).exists():
        file_size = Path(report_path).stat().st_size
        print(f"✅ Performance test completed")
        print(f"📊 Dataset: 50 producers, 20 consumers")
        print(f"⏱️  Total time: {total_time:.2f}s")
        print(f"⏱️  Generation time: {generation_time:.2f}s")
        print(f"📄 File size: {file_size:,} bytes")
        print(f"🚀 Performance: {file_size/generation_time:,.0f} bytes/second")
    else:
        print("❌ Performance test failed")


if __name__ == "__main__":
    # Run tests
    report_path = test_report_generation()
    
    if report_path:
        performance_test()
        
        print("\n" + "="*60)
        print("🎯 TESTING COMPLETE")
        print("="*60)
        print(f"📊 Main test report: {report_path}")
        print("🌐 Open the HTML file in your browser to see the interactive report")
        print("📱 The report is responsive and works on mobile devices")
        print("🔍 Use browser dev tools to inspect the generated charts and data")
    else:
        print("\n❌ Testing failed - please check the error messages above")
