"""
Usage Example for JIRA Processing Report Generator

This module demonstrates how to integrate the reporting system with
the existing JIRA processing pipeline.
"""

import asyncio
from datetime import datetime
from pathlib import Path
from logging import Logger

from .integration_patch import setup_metrics_tracking, global_processing_metrics
from .jira_processing_report_generator import create_report_at_exit


async def enhanced_process_jira_issues(project_key: str, scope: str, initial_load: bool,
                                     original_process_function, *args, **kwargs):
    """
    Enhanced version of process_jira_issues with comprehensive reporting.
    
    This function wraps the original process_jira_issues function to add
    automatic metrics collection and report generation.
    """
    
    # Setup metrics tracking and report generation
    print(f"🚀 Starting JIRA processing for {project_key} with enhanced reporting...")
    original_fetch = setup_metrics_tracking(project_key)
    
    try:
        # Call the original function
        result = await original_process_function(project_key, scope, initial_load, *args, **kwargs)
        
        print(f"✅ JIRA processing completed successfully for {project_key}")
        return result
        
    except Exception as e:
        print(f"❌ JIRA processing failed for {project_key}: {e}")
        raise
    
    finally:
        # Report will be automatically generated at exit due to atexit handler
        print(f"📊 Report generation scheduled for {project_key}")


def manual_report_generation_example():
    """
    Example of manually generating a report with sample data.
    This can be used for testing or when you want to generate reports
    outside of the normal processing flow.
    """
    from .jira_processing_report_generator import (
        MetricsCollector, JiraProcessingReportGenerator,
        ProducerMetrics, ConsumerMetrics, ApiCallMetrics
    )
    
    # Create sample metrics
    collector = MetricsCollector()
    
    # Sample producer metrics
    producer1 = ProducerMetrics(
        producer_name="producer_0",
        jql_query="project = PLAT AND updated >= '2024-01-01'",
        total_records_processed=1250,
        total_records_estimate=1200,
        difference_percentage=4.17,
        processing_time=45.2,
        waiting_time=2.1,
        cpu_time=42.8,
        start_time=datetime.now(),
        end_time=datetime.now(),
        status="completed"
    )
    
    producer2 = ProducerMetrics(
        producer_name="producer_1",
        jql_query="project = PLAT AND updated >= '2024-01-15'",
        total_records_processed=890,
        total_records_estimate=900,
        difference_percentage=1.11,
        processing_time=32.7,
        waiting_time=1.5,
        cpu_time=30.9,
        start_time=datetime.now(),
        end_time=datetime.now(),
        status="completed"
    )
    
    collector.add_producer_metrics(producer1)
    collector.add_producer_metrics(producer2)
    
    # Sample consumer metrics
    consumer1 = ConsumerMetrics(
        consumer_name="consumer_apple",
        consumer_type="consume_issues",
        messages_received=2140,
        messages_sent=2140,
        processing_time=78.5,
        scaling_events=[
            {"timestamp": datetime.now(), "event": "scaled_up", "reason": "queue_size_exceeded"},
            {"timestamp": datetime.now(), "event": "scaled_down", "reason": "queue_empty"}
        ],
        start_time=datetime.now(),
        end_time=datetime.now(),
        status="completed"
    )
    
    consumer2 = ConsumerMetrics(
        consumer_name="changelog_processor",
        consumer_type="consume_changelog",
        messages_received=856,
        messages_sent=856,
        processing_time=23.4,
        scaling_events=[],
        start_time=datetime.now(),
        end_time=datetime.now(),
        status="completed"
    )
    
    collector.add_consumer_metrics(consumer1)
    collector.add_consumer_metrics(consumer2)
    
    # Sample API metrics
    api_metrics = ApiCallMetrics(
        endpoint="api/3/search/jql",
        total_requests=45,
        successful_without_retries=38,
        successful_with_retries=5,
        failed_requests=2,
        average_time_excluding_wait=0.245,
        average_time_including_wait=0.312,
        retry_distribution={0: 38, 1: 4, 2: 1, 3: 2},
        error_types={"TIMEOUT": 1, "HTTP_ERROR": 1}
    )
    
    collector.api_metrics["api/3/search/jql"] = api_metrics
    collector.finalize()
    
    # Generate report
    generator = JiraProcessingReportGenerator(collector, "PLAT")
    report_path = generator.generate_html_report()
    
    print(f"📊 Sample report generated: {report_path}")
    return report_path


def integration_instructions():
    """
    Print instructions for integrating the reporting system.
    """
    instructions = """
    🔧 INTEGRATION INSTRUCTIONS FOR JIRA PROCESSING REPORTS
    
    To integrate the comprehensive reporting system with your existing JIRA processing:
    
    1. AUTOMATIC INTEGRATION (Recommended):
       Add this to the beginning of your process_jira_issues function:
       
       ```python
       from dags.data_pipeline.reports.integration_patch import setup_metrics_tracking
       
       async def process_jira_issues(project_key: str, scope: str, initial_load: bool):
           # Setup metrics tracking
           setup_metrics_tracking(project_key)
           
           # Your existing code continues here...
       ```
    
    2. MANUAL TRACKING:
       Use the tracking functions directly in your code:
       
       ```python
       from dags.data_pipeline.reports.jira_processing_report_generator import (
           track_producer_metrics, track_consumer_metrics, track_api_call
       )
       
       # Track producer
       track_producer_metrics(
           producer_name="producer_0",
           jql_query="project = PLAT",
           total_records_processed=1000,
           total_records_estimate=950,
           processing_time=45.2,
           waiting_time=2.1,
           cpu_time=42.8,
           start_time=start_time,
           end_time=end_time
       )
       ```
    
    3. FETCH_WITH_RETRIES INTEGRATION:
       The system automatically patches fetch_with_retries when you call
       setup_metrics_tracking(). No additional code changes needed.
    
    4. REPORT GENERATION:
       Reports are automatically generated at process exit. You can also
       generate them manually:
       
       ```python
       from dags.data_pipeline.reports.jira_processing_report_generator import create_report_at_exit
       
       report_path = create_report_at_exit("PLAT", Path("./reports"))
       ```
    
    5. REPORT FEATURES:
       - Executive Summary: High-level KPIs with color-coded status
       - Producer Details: JQL queries, accuracy, timing metrics
       - Consumer Details: Message throughput, scaling events
       - API Metrics: Success rates, retry distribution, error analysis
       - Interactive Charts: Plotly-based visualizations
       - Responsive Design: Works on desktop and mobile
       - Exportable HTML: Self-contained file with all dependencies
    
    6. CUSTOMIZATION:
       Modify the CSS and chart configurations in:
       - jira_processing_report_generator.py: Core report generation
       - metrics_integration.py: Context managers and decorators
       - integration_patch.py: Automatic patching system
    
    📊 The reports provide both executive-level summaries and operational details
    for comprehensive monitoring of your JIRA data processing pipeline.
    """
    
    print(instructions)


if __name__ == "__main__":
    # Generate a sample report for demonstration
    print("🧪 Generating sample report...")
    report_path = manual_report_generation_example()
    
    print("\n" + "="*60)
    integration_instructions()
    print("="*60)
    
    print(f"\n✨ Sample report available at: {report_path}")
    print("Open this file in your browser to see the interactive report!")
