flowchart TD
    classDef queue fill:#fce5cd,stroke:#d35400,stroke-width:2px,rx:10,ry:10
    classDef worker fill:#d5f5e3,stroke:#1e8449,stroke-width:2px,rx:5,ry:5
    classDef group fill:#d6eaf8,stroke:#154360,stroke-width:2px,rx:5,ry:5
    classDef db fill:#fadbd8,stroke:#922b21,stroke-width:3px,rx:10,ry:10
    classDef stage fill:#e8f8f5,stroke:#1abc9c,stroke-width:1px,stroke-dasharray: 5 5
    classDef coordinator fill:#fff2cc,stroke:#d6b656,stroke-width:3px,rx:15,ry:15
    classDef scaler fill:#f8cecc,stroke:#b85450,stroke-width:2px,rx:10,ry:10

    subgraph COORD ["🎛️ Scalable Coordination System"]
        direction TB
        CM[📡 Coordination Manager<br/>🔄 Event-based Coordination<br/>📊 Producer/Consumer Tracking]:::coordinator
        FS[📈 Fibonacci Scaler<br/>🔢 1597→1, 2584→2, ..., >1346269→15<br/>📏 Dynamic Batch Sizing]:::scaler
        DCM[⚖️ Dynamic Consumer Manager<br/>📊 Queue Size Monitoring<br/>🔄 Auto-scaling >50 msgs]:::scaler
    end

    subgraph S1 ["🎯 Stage 1: Scalable Issue Collection"]
        direction TB
        P[📥 Dynamic Producers<br/>🔢 Fibonacci-based Scaling: 1-15<br/>📊 Event-coordinated Termination]:::worker
        --> Q1[⚡ Main Priority Queue<br/>📊 Issue Objects<br/>🏆 FIFO + Priority + Events]:::queue
        Q1 --> C[🔄 Event-aware Consumers<br/>📡 Coordination-based Termination<br/>🎯 Parse & Route Issues]:::worker
    end
    
    subgraph S2 ["🔀 Stage 2: Dynamic Fan-out Processing"]
        direction TB
        subgraph Queues ["Auto-scaling Specialized Queues"]
            QA[📋 Issue Queue<br/>🎯 Core Issue Data<br/>⚡ Early Processing Trigger]:::queue
            QB[⏱️ Worklog Queue<br/>📊 Time Tracking<br/>🔄 Dynamic Consumers: 1-3]:::queue
            QC[📝 Changelog Queue<br/>📚 History Data<br/>🔄 Dynamic Consumers: 1-3]:::queue
            QD[🔗 IssueLinks Queue<br/>🔗 Relationship Data<br/>🔄 Dynamic Consumers: 1-3]:::queue
            QE[💬 Comments Queue<br/>💬 Discussion Data<br/>🔄 Dynamic Consumers: 1-3]:::queue
        end

        subgraph Workers ["Auto-scaling Processing Workers"]
            CA[🏭 consume_issue<br/>🎯 Transform Core Data<br/>📡 Signals Early Processing]:::worker
            CB[🏭 consume_worklog<br/>⏱️ Parse Time Entries<br/>📊 Queue-size Aware]:::worker
            CC[🏭 consume_changelog<br/>📝 Track Changes<br/>📊 Queue-size Aware]:::worker
            CD[🏭 consume_issue_links<br/>🔗 Map Relations<br/>📊 Queue-size Aware]:::worker
            CE[🏭 consume_comments<br/>💬 Extract Discussions<br/>📊 Queue-size Aware]:::worker
        end
    end
    
    subgraph S3 ["💾 Stage 3: Protocol-based Data Persistence with Referential Integrity"]
        direction TB

        subgraph PROTOCOLS ["🔌 Protocol-based Architecture"]
            QP[📋 QueueProcessorProtocol<br/>🎯 Simplified Interface<br/>🔄 No Complex ABC]:::protocol
            DFP[📊 DataFrameProcessorProtocol<br/>🔗 Referential Integrity<br/>📈 Batch Processing]:::protocol
            RIP[🔒 ReferentialIntegrityProtocol<br/>👨‍👩‍👧‍👦 Parent-Child Coordination<br/>⏱️ Commitment Signaling]:::protocol
        end

        subgraph PROCESSORS ["🏭 Simplified Processors"]
            IQP[🎯 IssueQueueProcessor<br/>📊 Issue Table Parent<br/>🔄 Hierarchy Processing<br/>📡 Signals Commitment]:::processor
            OQP[🔗 OthersQueueProcessor<br/>👶 Child Tables<br/>⏳ Waits for Parent<br/>🔒 Referential Safety]:::processor
        end

        QF[🎯 Final Upsert Queues<br/>📤 queue_upsert_issue<br/>📤 queue_upsert_others<br/>🔄 Protocol-based Processing]:::queue
        --> PgDB[🐘 PostgreSQL<br/>📊 Data Warehouse<br/>🔄 UPSERT Operations<br/>🔒 Referential Integrity]:::db
    end
    
    %% Coordination System connections
    COORD -.->|Controls Scaling| S1
    COORD -.->|Manages Consumers| S2
    COORD -.->|Triggers Processing| S3

    %% Stage 1 to Stage 2 connections
    C -.->|Event-coordinated Routing| QA
    C -.->|Event-coordinated Routing| QB
    C -.->|Event-coordinated Routing| QC
    C -.->|Event-coordinated Routing| QD
    C -.->|Event-coordinated Routing| QE

    %% Stage 2 internal connections with dynamic scaling
    QA -->|Dynamic Scaling| CA
    QB -->|Dynamic Scaling| CB
    QC -->|Dynamic Scaling| CC
    QD -->|Dynamic Scaling| CD
    QE -->|Dynamic Scaling| CE

    %% Stage 2 to Stage 3 connections with referential integrity
    CA -.->|Priority Data + Parent Table| QF
    CB -.->|Child Table Data| QF
    CC -.->|Child Table Data| QF
    CD -.->|Child Table Data| QF
    CE -.->|Child Table Data| QF

    %% Protocol-based processing flow
    QF --> IQP
    QF --> OQP
    IQP -.->|Parent Committed Signal| OQP
    IQP --> PgDB
    OQP --> PgDB
    
    %% Add enhanced metrics/monitoring boxes
    M1[📊 Enhanced Metrics<br/>• Real-time Queue Depths<br/>• Dynamic Scaling Events<br/>• Processing Rates<br/>• Producer/Consumer Counts<br/>• Event Coordination Status]:::group
    M1 -.-> COORD
    M1 -.-> S1
    M1 -.-> S2
    M1 -.-> S3

    %% Enhanced error handling
    E1[⚠️ Enhanced Error Handling<br/>• Event-based Recovery<br/>• Graceful Scaling Down<br/>• Coordination-aware Retries<br/>• Circuit Breakers]:::group
    E1 -.-> COORD
    E1 -.-> S2

    %% Scaling indicators
    SCALE[🔄 Scaling Rules<br/>📈 Fibonacci: 1597→1, 2584→2, ...<br/>📊 Queue >50 → +1 Consumer<br/>⚡ Early Processing Triggers<br/>🎯 Event-based Termination]:::scaler
    SCALE -.-> COORD

    %% Class definitions
    classDef coordinator fill:#e1f5fe,stroke:#01579b,stroke-width:2px,color:#000
    classDef scaler fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000
    classDef worker fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px,color:#000
    classDef queue fill:#fff3e0,stroke:#e65100,stroke-width:2px,color:#000
    classDef db fill:#fce4ec,stroke:#880e4f,stroke-width:2px,color:#000
    classDef protocol fill:#e3f2fd,stroke:#0277bd,stroke-width:3px,color:#000
    classDef processor fill:#f1f8e9,stroke:#33691e,stroke-width:3px,color:#000
    classDef group fill:#f5f5f5,stroke:#757575,stroke-width:1px,color:#000