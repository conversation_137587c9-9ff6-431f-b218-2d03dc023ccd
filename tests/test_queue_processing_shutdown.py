#!/usr/bin/env python3
# coding=utf-8
"""
Test suite for queue processing shutdown scenarios.
This test verifies that queue processing handles cancellation and shutdown
correctly without triggering "task_done() called too many times" errors.
"""

import asyncio
import logging
import os
import signal
import sys
import time
import unittest
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock, MagicMock

import aiohttp
import pytest

from dags.data_pipeline.debug.debug_utils import debug_queue_operation

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Set up test environment
os.environ.setdefault("ENABLE_DEBUG_MONITOR", "true")
os.environ.setdefault("DEBUG_MONITOR_INTERVAL", "5")

try:
    from dags.data_pipeline.queue_processors.specialized_consumers import BaseQueueProcessor
    from dags.data_pipeline.custom_logger import CorrelationContext
    from dags.data_pipeline.utility_code import setup_signal_handlers
except ImportError as e:
    print(f"⚠️ Import error: {e}")
    print("Some modules may not be available for testing")


class TestQueueProcessingShutdown:
    """Test suite for queue processing shutdown scenarios."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.test_queue = asyncio.Queue()
        self.shutdown_event = asyncio.Event()
        
    def teardown_method(self):
        """Clean up after tests."""
        if hasattr(self, 'test_queue'):
            # Clear any remaining items
            while not self.test_queue.empty():
                try:
                    self.test_queue.get_nowait()
                    self.test_queue.task_done()
                except asyncio.QueueEmpty:
                    break
    
    @pytest.mark.asyncio
    async def test_queue_get_cancellation_handling(self):
        """Test that queue get cancellation doesn't cause task_done mismatch."""
        print("🧪 Testing queue get cancellation handling...")
        
        # Create a queue with no items
        empty_queue = asyncio.Queue()
        
        # This should not raise "task_done() called too many times"
        async def test_cancelled_get():
            try:
                async with debug_queue_operation(empty_queue, "get", "test_queue") as item:
                    # This will block forever since queue is empty
                    # await empty_queue.get()  # This is done by the context manager
                    empty_queue.task_done()  # Should not be reached
            except asyncio.CancelledError:
                # This is expected when cancelled
                print("   ✅ Queue get was cancelled as expected")
                raise
        
        # Start the get operation
        task = asyncio.create_task(test_cancelled_get())
        
        # Wait a bit then cancel
        await asyncio.sleep(0.1)
        task.cancel()
        
        # Verify cancellation was handled correctly
        with pytest.raises(asyncio.CancelledError):
            await task
        
        print("   ✅ No task_done mismatch after cancellation")
    
    @pytest.mark.asyncio
    async def test_queue_processor_cancellation(self):
        """Test BaseQueueProcessor handles cancellation gracefully."""
        print("🧪 Testing BaseQueueProcessor cancellation...")
        
        # if 'BaseQueueProcessor' not in globals():
        #     print("   ⚠️ BaseQueueProcessor not available, skipping test")
        #     return
        
        # Create a test processor
        class TestProcessor(BaseQueueProcessor):
            def __init__(self):
                super().__init__()
                self.processed_items = []
                self.shutdown_called = False

            async def process_queue_item(self, item, queue_upsert_issue, http_session, my_logger=None):
                self.processed_items.append(item)
                await asyncio.sleep(0.01)  # Simulate processing
                return True

            async def process_item(self, item):
                self.processed_items.append(item)
                await asyncio.sleep(0.01)  # Simulate processing
                return item
            
            async def shutdown(self):
                self.shutdown_called = True


        # Setup queues
        input_queue = asyncio.Queue()
        upsert_queue = asyncio.Queue()

        # Add some items to process
        for i in range(5):
            await input_queue.put(f"item_{i}")

        processor = TestProcessor()

        # Mock aiohttp session and logger
        session = MagicMock(spec=aiohttp.ClientSession)
        logger = MagicMock()

        # Start processor in a task
        processor_task = asyncio.create_task(
            processor.run_processor(
                queue_id=1,
                name="test",
                input_queue=input_queue,
                queue_upsert_issue=upsert_queue,
                http_session=session,
                my_logger=logger
            )
        )

        # Let it process some items
        await asyncio.sleep(0.1)
        
        # Cancel the processor
        processor_task.cancel()
        
        # Verify cancellation was handled
        try:
            await processor_task
        except asyncio.CancelledError:
            print("   ✅ Processor was cancelled as expected")

        # Assert some items were processed
        assert len(processor.processed_items) > 0
        print(f"   ✅ Processed {len(processor.processed_items)} items before cancellation")
        
        # Verify shutdown was called
        await processor.shutdown()
        assert processor.shutdown_called, "Shutdown should have been called"
        print(f"   ✅ Processed {len(processor.processed_items)} items before cancellation")
    
    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_multiple_queue_operations_with_cancellation(self):
        """Test multiple concurrent queue operations with cancellation."""
        print("🧪 Testing multiple queue operations with cancellation...")
        
        test_queue = asyncio.Queue(maxsize=10)
        
        async def producer(producer_id):
            """Producer that adds items to queue."""
            try:
                for i in range(20):
                    item = f"producer_{producer_id}_item_{i}"
                    async with debug_queue_operation(test_queue, "put", "multi_test_queue", item):
                        pass  # Operation is performed by the context manager
                    await asyncio.sleep(0.01)
            except asyncio.CancelledError:
                print(f"   Producer {producer_id} cancelled")
                raise
        
        async def consumer(consumer_id):
            """Consumer that processes items from queue."""
            processed = 0
            try:
                while True:
                    async with debug_queue_operation(test_queue, "get", "multi_test_queue") as item:
                        processed += 1
                        # Simulate processing
                        await asyncio.sleep(0.01)
                        test_queue.task_done()
            except asyncio.CancelledError:
                print(f"   Consumer {consumer_id} cancelled (processed {processed} items)")
                raise
        
        # Start multiple producers and consumers
        producers = [asyncio.create_task(producer(i)) for i in range(2)]
        consumers = [asyncio.create_task(consumer(i)) for i in range(3)]
        
        # Let them run for a bit
        await asyncio.sleep(0.5)
        
        # Cancel all tasks
        all_tasks = producers + consumers
        for task in all_tasks:
            task.cancel()
        
        # Wait for all to complete
        cancelled_count = 0
        for task in all_tasks:
            try:
                await task
            except asyncio.CancelledError:
                cancelled_count += 1
        
        print(f"   ✅ {cancelled_count}/{len(all_tasks)} tasks cancelled successfully")
        
        # Verify queue state is consistent
        remaining_items = test_queue.qsize()
        print(f"   ✅ Queue has {remaining_items} items remaining")
    
    @pytest.mark.asyncio
    async def test_signal_handling_integration(self):
        """Test signal handling integration with queue processing."""
        print("🧪 Testing signal handling integration...")
        
        if sys.platform == "win32":
            print("   ⚠️ Signal handling tests limited on Windows")
            return
        
        # Mock signal handling
        shutdown_requested = asyncio.Event()
        force_shutdown = asyncio.Event()
        
        def mock_signal_handler(signum, frame):
            if signum == signal.SIGINT:
                if shutdown_requested.is_set():
                    # Second Ctrl+C - force shutdown
                    force_shutdown.set()
                    print("   Force shutdown requested")
                else:
                    # First Ctrl+C - graceful shutdown
                    shutdown_requested.set()
                    print("   Graceful shutdown requested")
        
        # Set up mock signal handler
        old_handler = signal.signal(signal.SIGINT, mock_signal_handler)
        
        try:
            # Create a test queue processor
            test_queue = asyncio.Queue()
            
            async def queue_processor():
                """Simple queue processor that respects shutdown signals."""
                try:
                    while not shutdown_requested.is_set():
                        try:
                            # Use timeout to check shutdown periodically
                            try:
                                item = test_queue.get_nowait()
                                await asyncio.sleep(0.01)  # Process item
                                test_queue.task_done()
                            except asyncio.QueueEmpty:
                                await asyncio.sleep(0.1)  # Wait before checking again
                                continue  # No items available
                        except asyncio.CancelledError:
                            print("   Queue processor cancelled")
                            raise
                except asyncio.CancelledError:
                    print("   Queue processor handling cancellation")
                    raise
            
            # Start the processor
            processor_task = asyncio.create_task(queue_processor())
            
            # Add some items
            for i in range(5):
                await test_queue.put(f"item_{i}")
            
            # Wait a bit
            await asyncio.sleep(0.1)
            
            # Simulate first SIGINT (graceful shutdown)
            shutdown_requested.set()
            
            # Wait for graceful shutdown
            try:
                await asyncio.wait_for(processor_task, timeout=1.0)
                print("   ✅ Graceful shutdown completed")
            except asyncio.TimeoutError:
                # Force shutdown
                force_shutdown.set()
                processor_task.cancel()
                try:
                    await processor_task
                except asyncio.CancelledError:
                    print("   ✅ Force shutdown completed")
        
        finally:
            # Restore original signal handler
            signal.signal(signal.SIGINT, old_handler)
    
    @pytest.mark.asyncio
    @pytest.mark.stress
    async def test_stress_test_many_items(self):
        """Stress test with many items on queue and abrupt cancellation."""
        print("🧪 Running stress test with many items...")
        
        large_queue = asyncio.Queue()
        
        # Fill queue with many items
        item_count = 1000
        for i in range(item_count):
            await large_queue.put(f"stress_item_{i}")
        
        print(f"   Added {item_count} items to queue")
        
        processed_items = []
        
        async def stress_processor():
            """Processor that handles many items."""
            try:
                while True:
                    async with debug_queue_operation(large_queue, "get", "stress_queue") as item:
                        processed_items.append(item)
                        large_queue.task_done()
                        
                        # Small delay to simulate processing
                        await asyncio.sleep(0.001)
            except asyncio.CancelledError:
                print(f"   Stress processor cancelled after processing {len(processed_items)} items")
                raise
        
        # Start processor
        processor_task = asyncio.create_task(stress_processor())
        
        # Let it run for a short time
        await asyncio.sleep(0.5)
        
        # Abruptly cancel
        processor_task.cancel()
        
        try:
            await processor_task
        except asyncio.CancelledError:
            pass
        
        print(f"   ✅ Processed {len(processed_items)}/{item_count} items before cancellation")
        print(f"   ✅ Queue has {large_queue.qsize()} items remaining")
        
        # Verify no task_done mismatch
        assert large_queue.qsize() + len(processed_items) == item_count, \
            "Item count mismatch indicates task_done problem"
    
    @pytest.mark.asyncio
    async def test_performance_monitoring(self):
        """Test performance monitoring during queue operations."""
        print("🧪 Testing performance monitoring...")
        
        perf_queue = asyncio.Queue()
        
        # Track performance metrics
        metrics = {
            'operations': 0,
            'total_time': 0,
            'max_time': 0,
            'min_time': float('inf')
        }
        
        async def monitored_operation():
            """Operation with performance monitoring."""
            start_time = time.time()
            
            try:
                async with debug_queue_operation(perf_queue, "get", "perf_queue") as item:
                    # Simulate variable processing time
                    await asyncio.sleep(0.01 + (metrics['operations'] % 10) * 0.001)
                    perf_queue.task_done()
                
                # Record metrics
                operation_time = time.time() - start_time
                metrics['operations'] += 1
                metrics['total_time'] += operation_time
                metrics['max_time'] = max(metrics['max_time'], operation_time)
                metrics['min_time'] = min(metrics['min_time'], operation_time)
                
            except asyncio.CancelledError:
                print("   Performance monitoring cancelled")
                raise
        
        # Add items to process
        for i in range(50):
            await perf_queue.put(f"perf_item_{i}")
        
        # Start multiple monitored operations
        tasks = [asyncio.create_task(monitored_operation()) for _ in range(10)]
        
        # Let them run
        await asyncio.sleep(0.3)
        
        # Cancel remaining tasks
        for task in tasks:
            if not task.done():
                task.cancel()
        
        # Wait for all to complete
        await asyncio.gather(*tasks, return_exceptions=True)
        
        # Report performance metrics
        if metrics['operations'] > 0:
            avg_time = metrics['total_time'] / metrics['operations']
            print(f"   ✅ Performance metrics:")
            print(f"     - Operations: {metrics['operations']}")
            print(f"     - Average time: {avg_time*1000:.2f}ms")
            print(f"     - Min time: {metrics['min_time']*1000:.2f}ms")
            print(f"     - Max time: {metrics['max_time']*1000:.2f}ms")
        else:
            print("   ⚠️ No operations completed")
    
    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_debug_queue_operation_context_manager(self):
        """Test the debug_queue_operation context manager specifically."""
        print("🧪 Testing debug_queue_operation context manager...")
        
        if 'debug_queue_operation' not in globals():
            print("   ⚠️ debug_queue_operation not available, skipping test")
            return
        
        test_queue = asyncio.Queue()
        
        # Test successful operation
        async with debug_queue_operation(test_queue, "put", "context_test_queue", "test_item"):
            pass  # Operation is performed by the context manager
        
        # Test get operation
        async with debug_queue_operation(test_queue, "get", "context_test_queue") as item:
            assert item == "test_item"
            test_queue.task_done()
        
        print("   ✅ Normal operations work correctly")
        
        # Test cancelled operation
        async def cancelled_operation():
            async with debug_queue_operation(test_queue, "get", "context_test_queue") as item:
                # This should be cancelled before completing
                test_queue.task_done()  # Should not be reached
        
        task = asyncio.create_task(cancelled_operation())
        await asyncio.sleep(0.01)  # Let it start
        task.cancel()
        
        # This should not raise "task_done() called too many times"
        try:
            await task
        except asyncio.CancelledError:
            print("   ✅ Cancelled operation handled correctly")
        
        # Verify queue state is consistent
        assert test_queue.qsize() == 0, "Queue should be empty"
        print("   ✅ Queue state is consistent after cancellation")


async def run_all_tests():
    """Run all shutdown tests."""
    print("🚀 Queue Processing Shutdown Tests")
    print("=" * 60)
    
    # Create test instance
    test_instance = TestQueueProcessingShutdown()
    
    # List of test methods
    test_methods = [
        "test_queue_get_cancellation_handling",
        "test_queue_processor_cancellation", 
        "test_multiple_queue_operations_with_cancellation",
        "test_signal_handling_integration",
        "test_stress_test_many_items",
        "test_performance_monitoring",
        "test_debug_queue_operation_context_manager"
    ]
    
    passed = 0
    failed = 0
    
    for test_name in test_methods:
        print(f"\n📋 Running {test_name.replace('test_', '').replace('_', ' ').title()}...")
        
        try:
            # Set up test
            test_instance.setup_method()
            
            # Run test
            test_method = getattr(test_instance, test_name)
            await test_method()
            
            print(f"   ✅ {test_name} passed")
            passed += 1
            
        except Exception as e:
            print(f"   ❌ {test_name} failed: {e}")
            import traceback
            traceback.print_exc()
            failed += 1
        
        finally:
            # Clean up
            try:
                test_instance.teardown_method()
            except:
                pass
    
    print("\n" + "=" * 60)
    print(f"🎯 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All shutdown tests passed!")
        print("\nShutdown handling is working correctly with:")
        print("- ✅ Queue operation cancellation")
        print("- ✅ BaseQueueProcessor cancellation")
        print("- ✅ Signal handling integration")
        print("- ✅ Stress testing with many items")
        print("- ✅ Performance monitoring")
        print("- ✅ Debug context manager")
    else:
        print("⚠️ Some tests failed. Check the output above for details.")
    
    return failed == 0


if __name__ == "__main__":
    # Configure logging for tests
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    try:
        success = asyncio.run(run_all_tests())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Tests failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
